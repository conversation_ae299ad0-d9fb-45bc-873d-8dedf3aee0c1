# إعدادات JVM محسنة للتوافق مع Java 24 + Kotlin 2.1.0
org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=3G -XX:+UseG1GC -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en --enable-native-access=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED

# إعدادات Android
android.useAndroidX=true
android.enableJetifier=true

# إعدادات Kotlin 2.1.0 محسنة للعمل مع Java 24
kotlin.daemon.enabled=true
kotlin.daemon.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+UseG1GC --enable-native-access=ALL-UNNAMED
kotlin.compiler.execution.strategy=daemon
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.caching.enabled=true
kotlin.parallel.tasks.in.project=true

# تحسين الأداء مع Java 24
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.workers.max=6

# إعدادات Android محسنة (إزالة الإعدادات المهجورة)
# android.enableR8.fullMode=true (removed - deprecated in AGP 7.0+)

# إعدادات Java 24 المتقدمة
org.gradle.vfs.watch=true
org.gradle.unsafe.configuration-cache=true

# إعدادات Gradle محسنة لـ Java 21
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=false
org.gradle.caching=false
org.gradle.unsafe.configuration-cache=false

# إعدادات Kotlin محسنة - متوافق مع 2.1.0
kotlin.incremental=true
kotlin.parallel.tasks.in.project=true
kotlin.daemon.enabled=true
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G
org.gradle.kotlin.dsl.skipMetadataVersionCheck=true

# إعدادات إضافية لحل مشاكل Unicode
systemProp.file.encoding=UTF-8
systemProp.user.language=en
systemProp.user.country=US

# إعدادات Java 21 compatibility
org.gradle.warning.mode=all

# إعدادات إضافية لحل التحذيرات
android.suppressUnsupportedCompileSdk=35
android.suppressUnsupportedOptionWarnings=true
android.enableR8.fullMode=true
android.enableR8=true

# إعدادات تحسين البناء
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# تعطيل تحذيرات غير مهمة
systemProp.org.gradle.internal.http.connectionTimeout=120000
systemProp.org.gradle.internal.http.socketTimeout=120000
