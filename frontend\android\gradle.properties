# إعدادات JVM محسنة لـ Gradle 8.6 + Java 21 + Kotlin 2.1.0
org.gradle.jvmargs=-Xmx6G -XX:MaxMetaspaceSize=3G -XX:+UseG1GC -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Dkotlin.daemon.jvmargs=-Xmx2G

# إعدادات Android
android.useAndroidX=true
android.enableJetifier=true

# إعدادات Gradle محسنة لـ Java 21
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=false
org.gradle.caching=false
org.gradle.unsafe.configuration-cache=false

# إعدادات Kotlin محسنة - متوافق مع 2.1.0
kotlin.incremental=true
kotlin.parallel.tasks.in.project=true
kotlin.daemon.enabled=true
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G
org.gradle.kotlin.dsl.skipMetadataVersionCheck=true

# إعدادات إضافية لحل مشاكل Unicode
systemProp.file.encoding=UTF-8
systemProp.user.language=en
systemProp.user.country=US

# إعدادات Java 21 compatibility
org.gradle.warning.mode=all

# إعدادات إضافية لحل التحذيرات
android.suppressUnsupportedCompileSdk=35
android.suppressUnsupportedOptionWarnings=true
android.enableR8.fullMode=true
android.enableR8=true

# إعدادات تحسين البناء
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# تعطيل تحذيرات غير مهمة
systemProp.org.gradle.internal.http.connectionTimeout=120000
systemProp.org.gradle.internal.http.socketTimeout=120000
