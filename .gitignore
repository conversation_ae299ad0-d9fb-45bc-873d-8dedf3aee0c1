# ===================================
# منتجاتي - Montajati App
# ===================================

# ===================================
# Node.js Backend
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 🔐 Firebase Service Account (NEVER COMMIT!)
backend/firebase-service-account.json
firebase-service-account.json
*.log
logs/
pids/
*.pid
*.seed
*.pid.lock
.nyc_output
coverage/
.grunt
bower_components
.lock-wscript
build/Release
.node_repl_history
*.tgz
.yarn-cache/
.cache/

# ===================================
# Flutter Frontend
# ===================================
# Dart/Flutter
.dart_tool/
.packages
.pub-cache/
.pub/
build/
.flutter-plugins
.flutter-plugins-dependencies
.metadata
*.iml

# ⚠️ LARGE FILES - Flutter Build Artifacts
frontend/.dart_tool/
frontend/build/
frontend/android/app/build/
frontend/ios/build/
frontend/linux/build/
frontend/macos/build/
frontend/web/build/
frontend/windows/build/

# ⚠️ LARGE BINARY FILES
*.apk
*.dill
*.so
*.dylib

# Android
android/app/debug
android/app/profile
android/app/release
android/.gradle/
android/captures/
android/gradlew
android/gradlew.bat
android/local.properties
android/**/GeneratedPluginRegistrant.java
android/key.properties
*.jks

# iOS
ios/Flutter/flutter_assets/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/ServiceDefinitions.json
ios/Runner/GeneratedPluginRegistrant.*
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData/
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Web
web/dist/
web/build/

# Windows
windows/flutter/generated_plugin_registrant.cc
windows/flutter/generated_plugin_registrant.h

# macOS
macos/Flutter/GeneratedPluginRegistrant.swift

# Linux
linux/flutter/generated_plugin_registrant.cc
linux/flutter/generated_plugin_registrant.h

# ===================================
# IDE & Editor Files
# ===================================
# VS Code
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-snippets

# IntelliJ IDEA
.idea/workspace.xml
.idea/tasks.xml
.idea/usage.statistics.xml
.idea/dictionaries
.idea/shelf/
.idea/contentModel.xml
.idea/dataSources.ids
.idea/dataSources.local.xml
.idea/sqlDataSources.xml
.idea/dynamic.xml
.idea/uiDesigner.xml
.idea/dbnavigator.xml
.idea/gradle.xml
.idea/libraries
.idea/mongoSettings.xml
.idea/caches/

# ===================================
# Database & Environment
# ===================================
# 🔐 SECURITY: Environment files (NEVER COMMIT THESE!)
.env
.env*
!.env.example
.env.local
.env.production
.env.development
backend/.env
backend/.env*
*/.env
*/.env.*

# Database
*.db
*.sqlite
*.sqlite3
database.json

# ===================================
# Logs & Temporary Files
# ===================================
*.log
logs/
*.tmp
*.temp
.DS_Store
Thumbs.db
.directory
*.swp
*.swo
*~

# ===================================
# Firebase & Google Services
# ===================================
google-services.json
GoogleService-Info.plist
firebase-debug.log
.firebase/

# ===================================
# Supabase
# ===================================
supabase/.branches
supabase/.temp

# ===================================
# Augment Files
# ===================================
.augment
.augment-*

# ===================================
# Production & Deployment
# ===================================
dist/
build/
*.tar.gz
*.zip
deployment/
.vercel
.netlify

# ===================================
# OS Generated Files
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===================================
# Backup Files
# ===================================
*.bak
*.backup
*.old
*.orig
