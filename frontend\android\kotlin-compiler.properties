# إعدادات Kotlin Compiler - الحل النهائي لمشكلة Daemon
# Final Solution for Kotlin Daemon Issues

# تعطيل Kotlin Daemon نهائياً
kotlin.daemon.enabled=false
kotlin.compiler.execution.strategy=in-process

# تعطيل جميع ميزات التحسين
kotlin.incremental=false
kotlin.incremental.android=false
kotlin.caching.enabled=false
kotlin.parallel.tasks.in.project=false

# إعدادات أساسية فقط
kotlin.code.style=official
kotlin.mpp.stability.nowarn=true
kotlin.native.ignoreDisabledTargets=true

# إجبار استخدام in-process compilation
kotlin.compiler.execution.strategy=in-process

# إعدادات التحسين
kotlin.incremental=false
kotlin.parallel.tasks.in.project=false
kotlin.daemon.useFallbackStrategy=true

# إعدادات التوافق
kotlin.mpp.stability.nowarn=true
kotlin.native.ignoreDisabledTargets=true

# تعطيل التحذيرات غير المهمة
kotlin.compiler.suppressWarnings=true
kotlin.suppressGradlePluginWarnings=true

# إعدادات الأداء
kotlin.compiler.keepIncrementalCompilationCachesInMemory=false
kotlin.compiler.preciseCompilationResultsBackup=false
