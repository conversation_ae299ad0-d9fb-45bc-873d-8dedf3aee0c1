# إعدادات Kotlin 2.1.0 محسنة للعمل مع Java 24
# Optimized Kotlin 2.1.0 Settings for Java 24 Compatibility

# تفعيل Kotlin Daemon مع إعدادات Java 24
kotlin.daemon.enabled=true
kotlin.daemon.jvmargs=-Xmx4G,-XX:MaxMetaspaceSize=2G,-XX:+UseG1GC,--enable-native-access=ALL-UNNAMED
kotlin.compiler.execution.strategy=daemon

# تفعيل ميزات التحسين مع Kotlin 2.1.0
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.caching.enabled=true
kotlin.parallel.tasks.in.project=true

# إعدادات Kotlin 2.1.0 المتقدمة
kotlin.code.style=official
kotlin.mpp.stability.nowarn=true
kotlin.native.ignoreDisabledTargets=true
kotlin.compiler.suppressExperimentalICOptimizationsWarning=true

# إعدادات Java 24 المتوافقة
kotlin.jvm.target.validation.mode=warning

# إعدادات التحسين
kotlin.incremental=false
kotlin.parallel.tasks.in.project=false
kotlin.daemon.useFallbackStrategy=true

# إعدادات التوافق
kotlin.mpp.stability.nowarn=true
kotlin.native.ignoreDisabledTargets=true

# تعطيل التحذيرات غير المهمة
kotlin.compiler.suppressWarnings=true
kotlin.suppressGradlePluginWarnings=true

# إعدادات الأداء
kotlin.compiler.keepIncrementalCompilationCachesInMemory=false
kotlin.compiler.preciseCompilationResultsBackup=false
