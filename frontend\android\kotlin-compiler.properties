# إعدادات Kotlin Compiler لحل مشاكل Daemon
# Kotlin Compiler Settings to Resolve Daemon Issues

# تعطيل Kotlin Daemon نهائياً
kotlin.daemon.enabled=false
kotlin.compiler.execution.strategy=in-process

# إعدادات الذاكرة
kotlin.daemon.jvm.options=-Xmx2G,-XX:MaxMetaspaceSize=512M

# إعدادات التحسين
kotlin.incremental=false
kotlin.parallel.tasks.in.project=false
kotlin.daemon.useFallbackStrategy=true

# إعدادات التوافق
kotlin.mpp.stability.nowarn=true
kotlin.native.ignoreDisabledTargets=true

# تعطيل التحذيرات غير المهمة
kotlin.compiler.suppressWarnings=true
kotlin.suppressGradlePluginWarnings=true

# إعدادات الأداء
kotlin.compiler.keepIncrementalCompilationCachesInMemory=false
kotlin.compiler.preciseCompilationResultsBackup=false
