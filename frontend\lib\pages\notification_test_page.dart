import 'package:flutter/material.dart';
import '../services/admin_service.dart';
import '../services/fcm_service.dart';

class NotificationTestPage extends StatefulWidget {
  const NotificationTestPage({super.key});

  @override
  State<NotificationTestPage> createState() => _NotificationTestPageState();
}

class _NotificationTestPageState extends State<NotificationTestPage> {
  final _phoneController = TextEditingController();
  bool _isLoading = false;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 اختبار الإشعارات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات FCM
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📱 معلومات FCM',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    FutureBuilder<Map<String, dynamic>>(
                      future: _getFCMInfo(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          final info = snapshot.data!;
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('✅ مُهيأ: ${info['isInitialized'] ? 'نعم' : 'لا'}'),
                              Text('🔑 لديه Token: ${info['hasToken'] ? 'نعم' : 'لا'}'),
                              if (info['token'] != null)
                                Text('📋 Token: ${info['token'].toString().substring(0, 20)}...'),
                            ],
                          );
                        }
                        return const CircularProgressIndicator();
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // اختبار الإشعارات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🔔 اختبار الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: '05xxxxxxxx',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _testNotification,
                            icon: const Icon(Icons.send),
                            label: const Text('إرسال إشعار تجريبي'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _testOrderNotification,
                            icon: const Icon(Icons.shopping_cart),
                            label: const Text('إشعار طلب'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // النتائج
            if (_result.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '📊 النتيجة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_result),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _testNotification() async {
    if (_phoneController.text.trim().isEmpty) {
      setState(() {
        _result = '❌ يرجى إدخال رقم الهاتف';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = '⏳ جاري الإرسال...';
    });

    try {
      final success = await AdminService.testNotification(_phoneController.text.trim());
      setState(() {
        _result = success 
          ? '✅ تم إرسال الإشعار التجريبي بنجاح!'
          : '❌ فشل في إرسال الإشعار التجريبي';
      });
    } catch (e) {
      setState(() {
        _result = '❌ خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testOrderNotification() async {
    if (_phoneController.text.trim().isEmpty) {
      setState(() {
        _result = '❌ يرجى إدخال رقم الهاتف';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = '⏳ جاري الإرسال...';
    });

    try {
      await AdminService.sendGeneralNotification(
        customerPhone: _phoneController.text.trim(),
        title: '📦 تحديث حالة طلبك',
        message: 'تم تحديث حالة طلبك إلى: جاري التوصيل - هذا إشعار تجريبي',
        additionalData: {
          'type': 'order_status_test',
          'orderId': 'TEST-${DateTime.now().millisecondsSinceEpoch}',
          'newStatus': 'out_for_delivery',
        },
      );
      
      setState(() {
        _result = '✅ تم إرسال إشعار تحديث الطلب التجريبي!';
      });
    } catch (e) {
      setState(() {
        _result = '❌ خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }
}
