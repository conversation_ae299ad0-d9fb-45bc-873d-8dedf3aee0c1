// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDkrQtsrzTDMP9OcE8rqKDr9HESxqo-vvM',
    appId: '1:827926006456:web:8f9a2b3c4d5e6f7g8h9i0j',
    messagingSenderId: '827926006456',
    projectId: 'montajati-app-7767d',
    authDomain: 'montajati-app-7767d.firebaseapp.com',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDkrQtsrzTDMP9OcE8rqKDr9HESxqo-vvM',
    appId: '1:827926006456:android:1a2b3c4d5e6f7g8h9i0j1k',
    messagingSenderId: '827926006456',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDkrQtsrzTDMP9OcE8rqKDr9HESxqo-vvM',
    appId: '1:827926006456:ios:2b3c4d5e6f7g8h9i0j1k2l',
    messagingSenderId: '827926006456',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    iosBundleId: 'com.montajati.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDkrQtsrzTDMP9OcE8rqKDr9HESxqo-vvM',
    appId: '1:827926006456:macos:YOUR_MACOS_APP_ID',
    messagingSenderId: '827926006456',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    iosBundleId: 'com.montajati.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDkrQtsrzTDMP9OcE8rqKDr9HESxqo-vvM',
    appId: '1:827926006456:windows:YOUR_WINDOWS_APP_ID',
    messagingSenderId: '827926006456',
    projectId: 'montajati-app-7767d',
    authDomain: 'montajati-app-7767d.firebaseapp.com',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
  );
}
