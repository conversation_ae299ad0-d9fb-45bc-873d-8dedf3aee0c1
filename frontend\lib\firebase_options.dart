// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAyJztyuQ_t_ZIftJVwi_rXr9zHkvy2P1Y',
    appId: '1:684581846709:web:0ec869872cc0887acd48fc',
    messagingSenderId: '684581846709',
    projectId: 'montajati-app-7767d',
    authDomain: 'montajati-app-7767d.firebaseapp.com',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAyJztyuQ_t_ZIftJVwi_rXr9zHkvy2P1Y',
    appId: '1:684581846709:android:app_id_here',
    messagingSenderId: '684581846709',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAyJztyuQ_t_ZIftJVwi_rXr9zHkvy2P1Y',
    appId: '1:684581846709:ios:app_id_here',
    messagingSenderId: '684581846709',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    iosBundleId: 'com.montajati.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAyJztyuQ_t_ZIftJVwi_rXr9zHkvy2P1Y',
    appId: '1:684581846709:ios:app_id_here',
    messagingSenderId: '684581846709',
    projectId: 'montajati-app-7767d',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    iosBundleId: 'com.montajati.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAyJztyuQ_t_ZIftJVwi_rXr9zHkvy2P1Y',
    appId: '1:684581846709:web:0ec869872cc0887acd48fc',
    messagingSenderId: '684581846709',
    projectId: 'montajati-app-7767d',
    authDomain: 'montajati-app-7767d.firebaseapp.com',
    storageBucket: 'montajati-app-7767d.firebasestorage.app',
    measurementId: 'G-MEASUREMENT_ID',
  );
}
