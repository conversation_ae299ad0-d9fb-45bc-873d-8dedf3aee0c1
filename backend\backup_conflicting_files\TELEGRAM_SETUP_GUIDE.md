# 📱 دليل إعداد نظام التلغرام والمخزون المتكامل

## 🎯 نظرة عامة

تم دمج نظام التلغرام ومراقبة المخزون بشكل كامل في الخادم الرئيسي `production_server.js`. النظام يراقب المخزون تلقائياً ويرسل تنبيهات فورية عبر التلغرام.

---

## 🔧 الإعداد المطلوب

### 1️⃣ **متغيرات البيئة المطلوبة**

أضف هذه المتغيرات إلى ملف `.env` أو متغيرات البيئة في Render:

```env
# متغيرات التلغرام (مطلوبة)
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=-123456789
LOW_STOCK_THRESHOLD=5

# متغيرات موجودة (تأكد من وجودها)
SUPABASE_URL=https://fqdhskaolzfavapmoqdl.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGci...
NODE_ENV=production
PORT=3003
```

### 2️⃣ **إنشاء Telegram Bot**

1. **تحدث مع @BotFather في Telegram:**
   - أرسل `/newbot`
   - اختر اسم للبوت (مثل: "Montajati Stock Monitor")
   - اختر معرف للبوت (مثل: "montajati_stock_bot")
   - احفظ Bot Token

2. **إنشاء مجموعة للتنبيهات:**
   - أنشئ مجموعة جديدة في Telegram
   - أضف البوت للمجموعة كمدير
   - احصل على Chat ID باستخدام @userinfobot

---

## 🚀 الميزات المدمجة

### ✅ **المراقبة الفورية:**
- **مراقبة فورية:** كل دقيقة واحدة تلقائياً
- **مراقبة محسنة:** تفحص فقط المنتجات التي كميتها 10 أو أقل
- **مراقبة عند التحديث:** فورية عند تحديث المنتجات أو إنشاء الطلبات
- **تنبيهات ذكية:** تجنب التكرار والإزعاج

### ✅ **أنواع التنبيهات:**
- **انخفاض المخزون:** عند الوصول لـ 5 قطع (قابل للتخصيص)
- **نفاد المخزون:** عند الوصول لـ 0 قطع
- **تقارير يومية:** ملخص شامل للمخزون

### ✅ **API Endpoints المتاحة:**
```
GET  /api/telegram/test          # اختبار اتصال التلغرام
GET  /api/inventory/test         # اختبار نظام المراقبة
POST /api/inventory/monitor/:id  # مراقبة منتج محدد
POST /api/inventory/monitor-all  # مراقبة جميع المنتجات
POST /api/inventory/daily-report # إرسال تقرير يومي
POST /api/telegram/send          # إرسال رسالة مخصصة
```

### ✅ **Hooks التلقائية:**
```
POST /api/products/update-hook   # يتم استدعاؤه عند تحديث المنتجات
POST /api/orders/create-hook     # يتم استدعاؤه عند إنشاء الطلبات
POST /api/orders/cancel-hook     # يتم استدعاؤه عند إلغاء الطلبات
```

---

## 🧪 اختبار النظام

### 1️⃣ **اختبار شامل:**
```bash
cd backend
node test_integrated_telegram_system.js
```

### 2️⃣ **اختبار سريع:**
```bash
# اختبار اتصال التلغرام
curl http://localhost:3003/api/telegram/test

# اختبار نظام المراقبة
curl http://localhost:3003/api/inventory/test

# إرسال رسالة اختبار
curl -X POST http://localhost:3003/api/telegram/send \
  -H "Content-Type: application/json" \
  -d '{"message": "🧪 رسالة اختبار من النظام"}'
```

---

## 🔗 التكامل مع Frontend

### **في Flutter (inventory_service.dart):**

النظام مدمج بالفعل! الكود الموجود يستدعي:
```dart
// عند تحديث المخزون
_monitorProductStock(productId);
```

هذا يرسل طلب إلى:
```
POST /api/inventory/monitor/:productId
```

---

## 📊 منطق التنبيهات

### **انخفاض المخزون:**
- يتم إرسال تنبيه عند الوصول لـ **5 قطع بالضبط**
- لا يتم إرسال تنبيه مكرر للمنتج نفسه

### **نفاد المخزون:**
- يتم إرسال تنبيه عند الوصول لـ **0 قطع أو أقل**
- لا يتم إرسال تنبيه مكرر للمنتج نفسه

### **التقارير اليومية:**
- تتضمن إحصائيات شاملة
- قوائم بالمنتجات التي تحتاج إعادة تخزين
- يمكن إرسالها يدوياً أو تلقائياً

---

## 🛠️ استكشاف الأخطاء

### **المشاكل الشائعة:**

1. **البوت لا يرسل رسائل:**
   ```
   ❌ المشكلة: خطأ 403 Forbidden
   ✅ الحل: تأكد من إضافة البوت للمجموعة كمدير
   ```

2. **Chat ID خاطئ:**
   ```
   ❌ المشكلة: خطأ 400 Bad Request
   ✅ الحل: Chat ID للمجموعات يبدأ بـ "-"
   ```

3. **النظام لا يراقب تلقائياً:**
   ```
   ❌ المشكلة: لا توجد تنبيهات
   ✅ الحل: تأكد من تشغيل production_server.js
   ```

### **فحص السجلات:**
```bash
# في الخادم، ابحث عن هذه الرسائل:
✅ تم الاتصال بـ Telegram Bot بنجاح
✅ نظام مراقبة المخزون جاهز
⏰ تشغيل المراقبة الدورية للمخزون...
```

---

## 🎯 الخلاصة

### ✅ **ما تم إنجازه:**
- ✅ دمج كامل في الخادم الرئيسي
- ✅ مراقبة تلقائية كل 30 دقيقة
- ✅ تنبيهات فورية عند التحديثات
- ✅ API endpoints شاملة
- ✅ Hooks تلقائية للتطبيق
- ✅ اختبارات شاملة

### 🚀 **النظام جاهز للاستخدام:**
1. أضف متغيرات التلغرام
2. شغل الخادم
3. النظام سيعمل فورياً كل دقيقة!

### 📱 **التنبيهات ستصل عند:**
- انخفاض المخزون لـ 5 قطع بالضبط
- نفاد المخزون (0 قطع)
- التقارير اليومية (حسب الطلب)

### ⚡ **الأداء المحسن:**
- يفحص فقط المنتجات التي تحتاج مراقبة (كمية ≤ 10)
- رسائل سجل أقل لتجنب الإزعاج
- استجابة فورية للتغييرات

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من السجلات في الخادم
2. شغل اختبار النظام المتكامل
3. تأكد من صحة متغيرات البيئة
