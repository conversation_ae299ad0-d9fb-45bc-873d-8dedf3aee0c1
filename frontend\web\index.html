<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="montajati_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>montajati_app</title>
  <link rel="manifest" href="manifest.json">

  <!-- إشعارات المتصفح -->
  <script src="js/notifications.js"></script>

  <!-- Firebase Configuration -->
  <script type="module">
    // إعدادات Firebase (استبدل بقيم مشروعك)
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js';
    import { getMessaging } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging.js';

    const firebaseConfig = {
      apiKey: "AIzaSyCyNzfXZS5httUcJMZcQV0Q_wjyMPP7fw4",
      authDomain: "withdrawal-notifications.firebaseapp.com",
      projectId: "withdrawal-notifications",
      storageBucket: "withdrawal-notifications.firebasestorage.app",
      messagingSenderId: "684581846709",
      appId: "1:684581846709:web:0ec869872cc0887acd48fc"
    };

    // تهيئة Firebase
    const app = initializeApp(firebaseConfig);
    const messaging = getMessaging(app);

    // جعل messaging متاحاً عالمياً
    window.firebaseMessaging = messaging;
  </script>
</head>

<body>
  <script src="flutter_bootstrap.js" async></script>
</body>

</html>