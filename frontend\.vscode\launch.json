{"version": "0.2.0", "configurations": [{"name": "Flutter (Development with Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--debug"], "console": "debugConsole", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Web (Chrome - Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["-d", "chrome", "--web-port", "3002", "--debug"], "console": "debugConsole", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Android (Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--debug"], "console": "debugConsole", "deviceId": "android", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Release (Production Ready)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--release"], "console": "debugConsole"}]}