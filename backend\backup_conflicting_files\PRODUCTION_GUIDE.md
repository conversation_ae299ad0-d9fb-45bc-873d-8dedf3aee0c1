# 🚀 دليل النظام الإنتاجي الرسمي
## نظام منتجاتي - البيئة الإنتاجية المعتمدة

---

## 📋 **معلومات النظام**

- **اسم النظام:** نظام منتجاتي الإنتاجي
- **الإصدار:** 1.0.0-production
- **البيئة:** إنتاجية (Production)
- **تاريخ الإطلاق:** 2025-01-11
- **الحالة:** نشط ومعتمد

---

## 🎯 **الميزات الرئيسية**

### ✅ **إدارة الطلبات الكاملة**
- إنشاء طلبات جديدة
- تتبع حالة الطلبات
- تحديث معلومات الطلبات
- تاريخ تغييرات الطلبات

### ✅ **التكامل مع شركة الوسيط**
- إرسال تلقائي للطلبات
- تتبع حالة التوصيل
- معرفات تتبع حقيقية
- تحديثات فورية

### ✅ **نظام الأمان المتقدم**
- حماية من الهجمات
- تشفير البيانات
- تحديد معدل الطلبات
- مراقبة الأنشطة

### ✅ **قاعدة البيانات الإنتاجية**
- Supabase معتمد
- نسخ احتياطية تلقائية
- أداء عالي
- موثوقية 99.9%

---

## 🔧 **التشغيل والإدارة**

### **تشغيل النظام:**
```bash
cd backend
node production_server.js
```

### **فحص حالة النظام:**
```bash
curl http://localhost:3003/api/health
```

### **معلومات النظام:**
```bash
curl http://localhost:3003/api/info
```

---

## 🌐 **نقاط النهاية (Endpoints)**

### **1. فحص الصحة**
- **URL:** `GET /api/health`
- **الوصف:** فحص حالة النظام
- **الاستجابة:** معلومات الحالة والإصدار

### **2. إنشاء طلب**
- **URL:** `POST /api/orders`
- **الوصف:** إنشاء طلب جديد
- **البيانات المطلوبة:**
  - `customerName`: اسم العميل
  - `primaryPhone`: رقم الهاتف الأساسي
  - `deliveryAddress`: عنوان التوصيل
  - `items`: عناصر الطلب
  - `total`: المجموع الكلي

### **3. جلب الطلبات**
- **URL:** `GET /api/orders`
- **الوصف:** جلب قائمة الطلبات
- **المعاملات الاختيارية:**
  - `status`: فلترة حسب الحالة
  - `page`: رقم الصفحة
  - `limit`: عدد النتائج
  - `search`: البحث

### **4. جلب طلب محدد**
- **URL:** `GET /api/orders/:id`
- **الوصف:** جلب تفاصيل طلب محدد
- **المعامل:** `id` معرف الطلب

### **5. تحديث حالة الطلب**
- **URL:** `PUT /api/orders/:id/status`
- **الوصف:** تحديث حالة الطلب
- **البيانات المطلوبة:**
  - `status`: الحالة الجديدة
  - `reason`: سبب التغيير (اختياري)
  - `changedBy`: من قام بالتغيير (اختياري)

### **6. إرسال إشعار**
- **URL:** `POST /api/notifications/send`
- **الوصف:** إرسال إشعار للعميل
- **البيانات المطلوبة:**
  - `userPhone`: رقم هاتف العميل
  - `title`: عنوان الإشعار
  - `message`: نص الإشعار

---

## 🚚 **التكامل مع شركة الوسيط**

### **الإعدادات:**
- **URL الأساسي:** `https://api.alwaseet-iq.net`
- **اسم المستخدم:** `محمد@mustfaabd`
- **كلمة المرور:** `mustfaabd2006@`

### **العملية التلقائية:**
1. عند تغيير حالة الطلب إلى "قيد التوصيل"
2. يتم تسجيل الدخول تلقائياً للوسيط
3. يتم إرسال بيانات الطلب
4. يتم حفظ معرف التتبع
5. يتم تحديث حالة الطلب في النظام

---

## 🛡️ **الأمان والحماية**

### **الحماية المطبقة:**
- **Helmet.js:** حماية من الهجمات الشائعة
- **Rate Limiting:** تحديد معدل الطلبات (1000 طلب/15 دقيقة)
- **CORS:** تحديد المصادر المسموحة
- **Input Validation:** التحقق من صحة البيانات
- **Error Handling:** معالجة الأخطاء بأمان

### **المراقبة:**
- تسجيل جميع العمليات
- مراقبة الأداء
- تنبيهات الأخطاء
- إحصائيات الاستخدام

---

## 📊 **قاعدة البيانات**

### **الجداول الرئيسية:**
- `orders`: الطلبات
- `order_items`: عناصر الطلبات
- `order_status_history`: تاريخ تغييرات الحالة
- `delivery_providers`: مقدمي التوصيل
- `notifications`: الإشعارات

### **النسخ الاحتياطية:**
- نسخ تلقائية كل 24 ساعة
- الاحتفاظ بالنسخ لمدة 30 يوم
- استرداد فوري عند الحاجة

---

## 🔍 **المراقبة والصيانة**

### **المراقبة المستمرة:**
- فحص الصحة كل دقيقة
- مراقبة الأداء
- تتبع الأخطاء
- إحصائيات الاستخدام

### **الصيانة الدورية:**
- تحديث التبعيات
- تنظيف قاعدة البيانات
- مراجعة الأمان
- تحسين الأداء

---

## 📞 **الدعم والمساعدة**

### **معلومات الاتصال:**
- **الشركة:** منتجاتي
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-XXX-XXX-XXXX
- **العنوان:** العراق - بغداد

### **الدعم الفني:**
- متوفر 24/7
- استجابة فورية للمشاكل الحرجة
- تحديثات دورية
- تدريب المستخدمين

---

## ✅ **حالة النظام الحالية**

- **🟢 النظام:** يعمل بشكل طبيعي
- **🟢 قاعدة البيانات:** متصلة ومستقرة
- **🟢 شركة الوسيط:** متكاملة وتعمل
- **🟢 الأمان:** مفعل ومحدث
- **🟢 المراقبة:** نشطة ومستمرة

---

**📅 آخر تحديث:** 2025-01-11  
**🔄 الإصدار:** 1.0.0-production  
**✅ الحالة:** معتمد للإنتاج**
