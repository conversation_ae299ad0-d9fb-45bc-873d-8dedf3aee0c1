# 🔔 نظام الإشعارات الذكي المتكامل

## 📋 نظرة عامة

نظام إشعارات ذكي ومتكامل بالكامل يراقب تغييرات عمود `status` في جدول `orders` ويرسل إشعارات مستهدفة لكل مستخدم حسب طلباته فقط.

## ✨ المميزات الرئيسية

- **🎯 إشعارات مستهدفة**: كل مستخدم يحصل على إشعارات طلباته فقط
- **🚫 منع التكرار**: لا توجد إشعارات مكررة للحالة نفسها
- **🔄 إعادة المحاولة**: نظام ذكي لإعادة المحاولة عند الفشل
- **📊 مراقبة شاملة**: إحصائيات مفصلة ومراقبة الأداء
- **⚡ معالجة فورية**: استجابة فورية لتغييرات الحالة
- **🛡️ موثوقية عالية**: نظام قوي ومقاوم للأخطاء

## 🎨 أنواع الإشعارات

### 1. قيد التوصيل 🚗
```
العميل - قيد التوصيل 🚗
```

### 2. تم التوصيل 😊
```
العميل - تم التوصيل 😊
```

### 3. ملغي 😢
```
العميل - ملغي 😢
```

## 🏗️ بنية النظام

### 📊 قاعدة البيانات

#### جدول `notification_queue`
- قائمة انتظار الإشعارات المراد إرسالها
- يدعم الأولويات وإعادة المحاولة

#### جدول `notification_logs`
- سجل جميع الإشعارات المرسلة
- يمنع التكرار ويوفر إحصائيات

#### جدول `user_fcm_tokens`
- تخزين FCM tokens للمستخدمين
- ربط المستخدمين بأرقام هواتفهم

### 🔧 المكونات البرمجية

#### Database Trigger
- يراقب تغييرات عمود `status`
- ينشئ إشعارات ذكية تلقائياً

#### معالج قائمة الانتظار
- يعالج الإشعارات المعلقة
- يدير إعادة المحاولة والأخطاء

#### خدمة Firebase
- إرسال الإشعارات عبر FCM
- دعم Android و iOS

## 🚀 التثبيت والإعداد

### 1. متغيرات البيئة المطلوبة

```bash
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FIREBASE_SERVICE_ACCOUNT={"type":"service_account",...}
```

### 2. إعداد قاعدة البيانات

```bash
# إعداد النظام الكامل
npm run notification:setup

# أو إعداد يدوي
node setup_smart_notifications.js setup
```

### 3. تشغيل النظام

```bash
# تشغيل النظام الكامل
npm run notification:system

# أو تشغيل المعالج فقط
npm run notification:start
```

## 📊 المراقبة والإحصائيات

### عرض الإحصائيات
```bash
npm run notification:stats
```

### مراقبة الأداء
```bash
# إحصائيات مفصلة
node start_smart_notification_system.js stats

# إحصائيات قاعدة البيانات
node setup_smart_notifications.js stats
```

## 🧪 الاختبار

### اختبار إشعار مباشر
```bash
npm run notification:test ***********
```

### اختبار النظام الكامل
```bash
npm run notification:system-test ***********
```

### اختبار Database Trigger
```bash
node start_smart_notification_system.js test ***********
```

## 🔄 كيفية عمل النظام

### 1. مراقبة التغييرات
```sql
-- عند تحديث حالة الطلب
UPDATE orders SET status = 'in_delivery' WHERE id = 'ORDER123';

-- يتم تفعيل Trigger تلقائياً
-- ينشئ إشعار في notification_queue
```

### 2. معالجة الإشعارات
```javascript
// معالج قائمة الانتظار يعمل كل 5 ثواني
// يجلب الإشعارات المعلقة
// يرسلها عبر Firebase
// يسجل النتائج
```

### 3. منع التكرار
```javascript
// التحقق من عدم إرسال إشعار مماثل خلال آخر 5 دقائق
// لنفس الطلب ونفس المستخدم ونفس تغيير الحالة
```

## 📱 إعداد التطبيق (Frontend)

### تسجيل FCM Token
```dart
// في التطبيق، احفظ FCM Token عند تسجيل الدخول
await NotificationService.saveUserFCMToken(userPhone);
```

### استقبال الإشعارات
```dart
// إعداد معالج الإشعارات
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  // عرض الإشعار للمستخدم
});
```

## 🛠️ الصيانة والتحسين

### تنظيف البيانات القديمة
```sql
-- تشغيل دوري لحذف السجلات القديمة
SELECT cleanup_old_notification_data();
```

### مراقبة الأداء
- معدل نجاح الإرسال
- زمن الاستجابة
- عدد الإشعارات المعلقة

### تحسين الأداء
- فهرسة الجداول
- تحسين استعلامات قاعدة البيانات
- ضبط فترات المعالجة

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

#### لا تصل الإشعارات
```bash
# تحقق من FCM Token
npm run notification:test <phone_number>

# تحقق من قائمة الانتظار
npm run notification:stats
```

#### إشعارات مكررة
```bash
# تحقق من سجل الإشعارات
node setup_smart_notifications.js stats
```

#### أخطاء Firebase
```bash
# تحقق من متغيرات البيئة
node test-firebase-vars.js
```

### سجلات النظام
```bash
# مراقبة السجلات المباشرة
npm run notification:start

# عرض إحصائيات مفصلة
npm run notification:stats
```

## 📞 الدعم الفني

للحصول على المساعدة:
1. تحقق من السجلات والإحصائيات
2. اختبر النظام خطوة بخطوة
3. تأكد من صحة متغيرات البيئة
4. راجع دليل استكشاف الأخطاء

## 🔄 التحديثات المستقبلية

- دعم إشعارات إضافية (رسائل ترويجية، تذكيرات)
- واجهة إدارة الإشعارات
- تحليلات متقدمة للأداء
- دعم قنوات إشعارات متعددة

---

**✅ النظام جاهز للعمل بالكامل!**

استخدم `npm run notification:system` لبدء النظام الكامل.
